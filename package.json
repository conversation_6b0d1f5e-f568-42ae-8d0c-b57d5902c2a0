{"name": "ai-chrome-extension", "displayName": "Web助手", "version": "0.0.1", "description": "Web助手", "contributors": ["019327", "021961"], "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo build && plasmo package && node ./scripts/rename-zip.js"}, "dependencies": {"@ant-design/cssinjs": "1.18.2", "@ht/chatui": "1.0.0-beta.7", "@ht/xlog": "^4.1.0", "@plasmohq/redux-persist": "^6.1.0", "@plasmohq/storage": "^1.15.0", "@reduxjs/toolkit": "^2.0.1", "antd": "5.13.2", "axios": "^1.9.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^7.6.2", "redux": "^5.0.1", "redux-persist-webextension-storage": "^1.0.2", "webextension-polyfill-ts": "^0.26.0"}, "devDependencies": {"@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["<all_urls>"], "permissions": ["storage", "activeTab", "sidePanel", "contextMenus", "scripting", "tabs", "cookies", "alarms", "notifications", "downloads", "webRequest"], "background": {"service_worker": "background.ts"}, "web_accessible_resources": [{"resources": ["assets/*", "assets/**/*"], "matches": ["<all_urls>"]}]}}